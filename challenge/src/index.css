@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global Styles */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

/* Layer Base */
@layer base {
  :root {
    --background: hsl(0, 0%, 100%);
    --foreground: hsl(0, 0%, 3.9%);
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(0, 0%, 3.9%);
    --popover: hsl(0, 0%, 100%);
    --popover-foreground: hsl(0, 0%, 3.9%);
    --primary: hsl(0, 0%, 9%);
    --primary-foreground: hsl(0, 0%, 98%);
    --secondary: hsl(0, 0%, 96.1%);
    --secondary-foreground: hsl(0, 0%, 9%);
    --muted: hsl(0, 0%, 96.1%);
    --muted-foreground: hsl(0, 0%, 45.1%);
    --accent: hsl(0, 0%, 96.1%);
    --accent-foreground: hsl(0, 0%, 9%);
    --destructive: hsl(0, 84.2%, 60.2%);
    --destructive-foreground: hsl(0, 0%, 98%);
    --border: hsl(0, 0%, 89.8%);
    --input: hsl(0, 0%, 89.8%);
    --ring: hsl(0, 0%, 3.9%);
    --chart-1: hsl(12, 76%, 61%);
    --chart-2: hsl(173, 58%, 39%);
    --chart-3: hsl(197, 37%, 24%);
    --chart-4: hsl(43, 74%, 66%);
    --chart-5: hsl(27, 87%, 67%);
    --radius: 0.5rem;
  }
  .dark {
    --background: hsl(0, 0%, 3.9%);
    --foreground: hsl(0, 0%, 98%);
    --card: hsl(0, 0%, 3.9%);
    --card-foreground: hsl(0, 0%, 98%);
    --popover: hsl(0, 0%, 3.9%);
    --popover-foreground: hsl(0, 0%, 98%);
    --primary: hsl(0, 0%, 98%);
    --primary-foreground: hsl(0, 0%, 9%);
    --secondary: hsl(0, 0%, 14.9%);
    --secondary-foreground: hsl(0, 0%, 98%);
    --muted: hsl(0, 0%, 14.9%);
    --muted-foreground: hsl(0, 0%, 63.9%);
    --accent: hsl(0, 0%, 14.9%);
    --accent-foreground: hsl(0, 0%, 98%);
    --destructive: hsl(0, 62.8%, 30.6%);
    --destructive-foreground: hsl(0, 0%, 98%);
    --border: hsl(0, 0%, 14.9%);
    --input: hsl(0, 0%, 14.9%);
    --ring: hsl(0, 0%, 83.1%);
    --chart-1: hsl(220, 70%, 50%);
    --chart-2: hsl(160, 60%, 45%);
    --chart-3: hsl(30, 80%, 55%);
    --chart-4: hsl(280, 65%, 60%);
    --chart-5: hsl(340, 75%, 55%);
  }
}

@layer base {
  * {
    border-color: var(--border);
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
  }
}