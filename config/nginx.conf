pid /run/nginx.pid;
error_log /dev/stderr info;

events {
    worker_connections 1024;
}

http {
    server_tokens off;
    log_format docker '$remote_addr $remote_user $status "$request" "$http_referer" "$http_user_agent" ';
    access_log /dev/stdout docker;

    include /etc/nginx/mime.types;

    server {
        listen 1337;

        # Route for API endpoints
        location /api {
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header Host $http_host;
            proxy_pass http://127.0.0.1:3000;
        }

        # JWKS endpoint
        location /.well-known/jwks.json {
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header Host $http_host;
            proxy_pass http://127.0.0.1:3000/.well-known/jwks.json;
        }
        
        # Frontend application
        location / {
            proxy_http_version 1.1;                
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header X-Forwarded-For $remote_addr;
            proxy_set_header Host $http_host;
            proxy_pass http://127.0.0.1:5173;
        }
    }
}